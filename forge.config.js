/**
 * Reason for Function/Section: Configure Electron Forge for building portable Windows executables
 * Task Performed: Sets up packaging configuration, makers, and plugins for Squirrel.Windows
 * Linking Information: 
 *   - Link Type: Internal
 *   - Linked File Name: package.json (scripts section references this config)
 */
module.exports = {
  packagerConfig: {
    asar: true,  // Enable ASAR packaging for better performance and code protection
    name: "TaskPro",
    executableName: "TaskPro",
    platform: "win32",
    arch: "x64",
    out: "out",
    overwrite: true
  },
  makers: [
    {
      name: '@electron-forge/maker-squirrel',  // Windows installer using Squirrel framework
      config: {
        name: "TaskPro",
        setupExe: "TaskPro-Setup.exe"
      },
    }
  ],
  plugins: [
    {
      name: '@electron-forge/plugin-auto-unpack-natives',  // Handle native modules like SQLite
      config: {},
    }
  ]
};
