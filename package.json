{"name": "task-pro-desktop", "version": "1.0.0", "description": "Task Pro - Desktop Task Management Application", "main": "src/main.js", "homepage": "./", "scripts": {"start": "electron-forge start", "dev": "electron .", "package": "electron-forge package", "make": "electron-forge make", "build": "electron-forge make", "dist": "electron-forge make"}, "keywords": ["electron", "task-management", "supabase", "desktop-app"], "author": "Task Pro Team", "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "electron": "^28.0.0"}, "dependencies": {"@supabase/supabase-js": "^2.52.1", "electron-squirrel-startup": "^1.0.1", "framer-motion": "^10.16.16", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.303.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1"}}